#!/usr/bin/env python3
"""
Full-Stack Development Teacher Mentor
Interactive learning system for beginner developers
"""

import os
import json
import sys
from datetime import datetime
from pathlib import Path

class FullStackMentor:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.progress_file = self.base_dir / "progress" / "user_progress.json"
        self.curriculum_dir = self.base_dir / "curriculum"
        self.projects_dir = self.base_dir / "projects"
        
        # Ensure directories exist
        os.makedirs(self.base_dir / "progress", exist_ok=True)
        os.makedirs(self.curriculum_dir, exist_ok=True)
        os.makedirs(self.projects_dir, exist_ok=True)
        
        self.load_progress()
        
    def load_progress(self):
        """Load user progress from file"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                self.progress = json.load(f)
        else:
            self.progress = {
                "current_phase": 1,
                "current_lesson": 1,
                "completed_lessons": [],
                "completed_projects": [],
                "start_date": datetime.now().isoformat(),
                "total_study_time": 0
            }
            self.save_progress()
    
    def save_progress(self):
        """Save user progress to file"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)
    
    def display_welcome(self):
        """Display welcome message and menu"""
        print("\n" + "="*60)
        print("🎓 WELCOME TO YOUR FULL-STACK DEVELOPMENT MENTOR! 🎓")
        print("="*60)
        print("\nHello! I'm your personal coding mentor. I'll guide you")
        print("through learning full-stack development step by step,")
        print("with lots of hands-on projects and beginner-friendly explanations!")
        print("\n" + "-"*60)
        
    def display_menu(self):
        """Display main menu options"""
        print("\n📚 What would you like to do today?")
        print("\n1. 🚀 Continue Learning (Current Lesson)")
        print("2. 📊 View My Progress")
        print("3. 🗺️  View Learning Roadmap")
        print("4. 💻 Start a New Project")
        print("5. 📖 Review Previous Lessons")
        print("6. ❓ Get Help & Tips")
        print("7. 🎯 Set Learning Goals")
        print("8. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-8): ").strip()
        return choice
    
    def show_current_lesson(self):
        """Show current lesson based on progress"""
        phase = self.progress["current_phase"]
        lesson = self.progress["current_lesson"]
        
        print(f"\n📚 PHASE {phase} - LESSON {lesson}")
        print("-" * 40)
        
        # Define curriculum structure
        curriculum = {
            1: {
                1: {"title": "HTML Basics", "description": "Learn the building blocks of web pages"},
                2: {"title": "CSS Styling", "description": "Make your pages beautiful"},
                3: {"title": "JavaScript Fundamentals", "description": "Add interactivity"},
                4: {"title": "Git & Version Control", "description": "Professional development workflow"}
            },
            2: {
                1: {"title": "React Introduction", "description": "Component-based development"},
                2: {"title": "State & Props", "description": "Managing data in React"},
                3: {"title": "API Integration", "description": "Connecting to external data"},
                4: {"title": "React Router", "description": "Multi-page applications"}
            },
            3: {
                1: {"title": "Node.js Basics", "description": "Server-side JavaScript"},
                2: {"title": "Express Framework", "description": "Building web APIs"},
                3: {"title": "Database Integration", "description": "Storing and retrieving data"},
                4: {"title": "Authentication", "description": "User management and security"}
            }
        }
        
        if phase in curriculum and lesson in curriculum[phase]:
            lesson_info = curriculum[phase][lesson]
            print(f"📖 {lesson_info['title']}")
            print(f"📝 {lesson_info['description']}")
            
            print(f"\n🎯 Today's Learning Objectives:")
            self.show_lesson_objectives(phase, lesson)
            
            print(f"\n💡 Ready to start? (y/n): ", end="")
            if input().lower().startswith('y'):
                self.start_lesson(phase, lesson)
        else:
            print("🎉 Congratulations! You've completed the current curriculum!")
            print("Check back for more advanced topics coming soon!")
    
    def show_lesson_objectives(self, phase, lesson):
        """Show specific objectives for each lesson"""
        objectives = {
            (1, 1): [
                "Understand HTML structure and syntax",
                "Create your first webpage",
                "Learn about semantic HTML elements",
                "Build a simple personal page"
            ],
            (1, 2): [
                "Style HTML with CSS",
                "Learn about selectors and properties",
                "Create responsive layouts",
                "Style your personal page"
            ],
            (1, 3): [
                "Understand JavaScript basics",
                "Learn variables, functions, and events",
                "Add interactivity to your page",
                "Create dynamic content"
            ]
        }
        
        key = (phase, lesson)
        if key in objectives:
            for i, objective in enumerate(objectives[key], 1):
                print(f"   {i}. {objective}")
        else:
            print("   • Master the fundamentals")
            print("   • Build practical projects")
            print("   • Apply best practices")
    
    def start_lesson(self, phase, lesson):
        """Start a specific lesson"""
        print(f"\n🚀 Starting Phase {phase}, Lesson {lesson}...")
        print("\n" + "="*50)
        
        # Create lesson-specific content
        if phase == 1 and lesson == 1:
            self.teach_html_basics()
        elif phase == 1 and lesson == 2:
            self.teach_css_basics()
        elif phase == 1 and lesson == 3:
            self.teach_javascript_basics()
        else:
            print("📚 This lesson content is being prepared...")
            print("For now, let's create the project structure!")
            self.create_lesson_project(phase, lesson)
        
        # Mark lesson as completed
        lesson_key = f"phase_{phase}_lesson_{lesson}"
        if lesson_key not in self.progress["completed_lessons"]:
            self.progress["completed_lessons"].append(lesson_key)
            
        # Advance to next lesson
        if lesson < 4:
            self.progress["current_lesson"] = lesson + 1
        else:
            self.progress["current_phase"] = phase + 1
            self.progress["current_lesson"] = 1
            
        self.save_progress()
        print(f"\n✅ Lesson completed! Great job!")
    
    def teach_html_basics(self):
        """Teach HTML basics with hands-on example"""
        print("🌐 HTML BASICS - Let's build your first webpage!")
        print("\nHTML (HyperText Markup Language) is the backbone of every website.")
        print("Think of it as the skeleton that gives structure to web pages.")
        
        print("\n📝 Let's create your first HTML file:")
        
        # Create HTML example
        html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My First Webpage</title>
</head>
<body>
    <h1>Welcome to My Website!</h1>
    <p>This is my first webpage. I'm learning HTML!</p>
    
    <h2>About Me</h2>
    <p>I'm a beginner developer learning full-stack development.</p>
    
    <h2>My Goals</h2>
    <ul>
        <li>Learn HTML & CSS</li>
        <li>Master JavaScript</li>
        <li>Build amazing projects</li>
    </ul>
    
    <p><strong>Let's code together!</strong></p>
</body>
</html>'''
        
        # Save the HTML file
        project_dir = self.projects_dir / "phase1_lesson1"
        os.makedirs(project_dir, exist_ok=True)
        
        with open(project_dir / "index.html", 'w') as f:
            f.write(html_content)
        
        print(f"✅ Created: {project_dir}/index.html")
        print("\n🎯 Your Task:")
        print("1. Open the HTML file in your browser")
        print("2. Modify the content to make it about YOU")
        print("3. Add more sections (hobbies, favorite foods, etc.)")
        print("4. Experiment with different HTML tags")
        
        print(f"\n📍 File location: {project_dir}/index.html")
        print("\n💡 Tip: Right-click the file and 'Open with' your web browser!")
    
    def teach_css_basics(self):
        """Teach CSS basics with hands-on styling"""
        print("🎨 CSS BASICS - Let's make your webpage beautiful!")
        print("\nCSS (Cascading Style Sheets) controls how your HTML looks.")
        print("Think of HTML as the structure and CSS as the paint and decoration!")

        css_content = '''/* My First CSS File */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

h1 {
    color: #333;
    text-align: center;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    color: #007bff;
    margin-top: 30px;
}

p {
    color: #666;
    margin-bottom: 15px;
}

ul {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin-bottom: 10px;
    padding: 5px;
}

strong {
    color: #007bff;
}'''

        project_dir = self.projects_dir / "phase1_lesson2"
        os.makedirs(project_dir, exist_ok=True)

        with open(project_dir / "styles.css", 'w') as f:
            f.write(css_content)

        # Update HTML to include CSS
        html_with_css = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Styled Webpage</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>Welcome to My Styled Website!</h1>
    <p>Now my webpage has beautiful styling with CSS!</p>

    <h2>About Me</h2>
    <p>I'm learning how to make websites look amazing with CSS.</p>

    <h2>What I've Learned</h2>
    <ul>
        <li>HTML structure and elements</li>
        <li>CSS styling and colors</li>
        <li>How to link CSS to HTML</li>
        <li>Basic layout techniques</li>
    </ul>

    <p><strong>CSS makes everything look professional!</strong></p>
</body>
</html>'''

        with open(project_dir / "index.html", 'w') as f:
            f.write(html_with_css)

        print(f"✅ Created: {project_dir}/styles.css")
        print(f"✅ Created: {project_dir}/index.html")
        print("\n🎯 Your Task:")
        print("1. Open the new HTML file in your browser")
        print("2. Experiment with different colors in the CSS")
        print("3. Try changing fonts, sizes, and spacing")
        print("4. Add your own CSS rules!")

    def teach_javascript_basics(self):
        """Teach JavaScript basics with interactive examples"""
        print("⚡ JAVASCRIPT BASICS - Let's make it interactive!")
        print("\nJavaScript brings your webpage to life with interactivity.")
        print("It's the programming language of the web!")

        js_content = '''// My First JavaScript File
console.log("Hello, JavaScript!");

// Function to change the page title
function changeTitle() {
    document.title = "JavaScript is Awesome!";
    alert("Title changed! Check the browser tab.");
}

// Function to change text color
function changeColor() {
    const heading = document.querySelector('h1');
    const colors = ['red', 'blue', 'green', 'purple', 'orange'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    heading.style.color = randomColor;
}

// Function to add new content
function addContent() {
    const newParagraph = document.createElement('p');
    newParagraph.textContent = 'This paragraph was added with JavaScript!';
    newParagraph.style.backgroundColor = 'yellow';
    newParagraph.style.padding = '10px';
    document.body.appendChild(newParagraph);
}

// Function to show current time
function showTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    document.getElementById('time-display').textContent = `Current time: ${timeString}`;
}

// Update time every second
setInterval(showTime, 1000);'''

        project_dir = self.projects_dir / "phase1_lesson3"
        os.makedirs(project_dir, exist_ok=True)

        with open(project_dir / "script.js", 'w') as f:
            f.write(js_content)

        # Create interactive HTML
        interactive_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Interactive Webpage</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>My Interactive Website!</h1>
    <p>Click the buttons below to see JavaScript in action!</p>

    <div style="margin: 20px 0;">
        <button onclick="changeTitle()" style="margin: 5px; padding: 10px;">Change Title</button>
        <button onclick="changeColor()" style="margin: 5px; padding: 10px;">Change Color</button>
        <button onclick="addContent()" style="margin: 5px; padding: 10px;">Add Content</button>
    </div>

    <div id="time-display" style="font-size: 18px; font-weight: bold; margin: 20px 0;"></div>

    <h2>What I've Learned</h2>
    <ul>
        <li>HTML for structure</li>
        <li>CSS for styling</li>
        <li>JavaScript for interactivity</li>
        <li>How to make buttons work</li>
    </ul>

    <script src="script.js"></script>
</body>
</html>'''

        with open(project_dir / "index.html", 'w') as f:
            f.write(interactive_html)

        # Copy CSS from previous lesson
        css_content = '''body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background-color: #0056b3;
}'''

        with open(project_dir / "styles.css", 'w') as f:
            f.write(css_content)

        print(f"✅ Created: {project_dir}/script.js")
        print(f"✅ Created: {project_dir}/index.html")
        print(f"✅ Created: {project_dir}/styles.css")
        print("\n🎯 Your Task:")
        print("1. Open the HTML file and click all the buttons!")
        print("2. Look at the JavaScript code and try to understand it")
        print("3. Add your own button with a new function")
        print("4. Experiment with different JavaScript features")

    def show_progress(self):
        """Display user's learning progress"""
        print("\n📊 YOUR LEARNING PROGRESS")
        print("="*40)

        total_lessons = 12  # Adjust based on curriculum
        completed = len(self.progress["completed_lessons"])
        percentage = (completed / total_lessons) * 100

        print(f"📚 Lessons Completed: {completed}/{total_lessons}")
        print(f"📈 Progress: {percentage:.1f}%")
        print(f"🎯 Current Phase: {self.progress['current_phase']}")
        print(f"📖 Current Lesson: {self.progress['current_lesson']}")

        # Progress bar
        bar_length = 20
        filled = int(bar_length * percentage / 100)
        bar = "█" * filled + "░" * (bar_length - filled)
        print(f"Progress: [{bar}] {percentage:.1f}%")

        print(f"\n🗓️  Started: {self.progress['start_date'][:10]}")

        if self.progress["completed_projects"]:
            print(f"\n🏆 Completed Projects:")
            for project in self.progress["completed_projects"]:
                print(f"   ✅ {project}")

    def show_roadmap(self):
        """Display the complete learning roadmap"""
        print("\n🗺️  FULL-STACK DEVELOPMENT ROADMAP")
        print("="*50)

        roadmap = {
            "Phase 1: Web Fundamentals": [
                "HTML Basics & Structure",
                "CSS Styling & Layout",
                "JavaScript Fundamentals",
                "Git & Version Control"
            ],
            "Phase 2: Frontend Development": [
                "React Components & JSX",
                "State Management & Props",
                "API Integration & Fetch",
                "React Router & Navigation"
            ],
            "Phase 3: Backend Development": [
                "Node.js & Server Basics",
                "Express.js Framework",
                "Database Integration",
                "Authentication & Security"
            ],
            "Phase 4: Full-Stack Projects": [
                "Frontend-Backend Integration",
                "Deployment & Hosting",
                "Testing & Quality Assurance",
                "Performance Optimization"
            ]
        }

        current_phase = self.progress["current_phase"]

        for i, (phase_name, lessons) in enumerate(roadmap.items(), 1):
            status = "🟢" if i < current_phase else "🔵" if i == current_phase else "⚪"
            print(f"\n{status} {phase_name}")

            for j, lesson in enumerate(lessons, 1):
                lesson_key = f"phase_{i}_lesson_{j}"
                completed = "✅" if lesson_key in self.progress["completed_lessons"] else "📝"
                print(f"   {completed} {lesson}")

    def start_new_project(self):
        """Start a new project based on current level"""
        print("\n💻 PROJECT BUILDER")
        print("="*30)

        projects = {
            1: {
                "name": "Personal Portfolio Website",
                "description": "Build a complete portfolio showcasing your skills",
                "technologies": ["HTML", "CSS", "JavaScript"],
                "difficulty": "Beginner"
            },
            2: {
                "name": "Todo List App",
                "description": "Interactive task management application",
                "technologies": ["React", "CSS", "Local Storage"],
                "difficulty": "Intermediate"
            },
            3: {
                "name": "Blog API",
                "description": "RESTful API for a blogging platform",
                "technologies": ["Node.js", "Express", "Database"],
                "difficulty": "Intermediate"
            }
        }

        phase = self.progress["current_phase"]
        if phase in projects:
            project = projects[phase]
            print(f"🎯 Recommended Project: {project['name']}")
            print(f"📝 Description: {project['description']}")
            print(f"🛠️  Technologies: {', '.join(project['technologies'])}")
            print(f"📊 Difficulty: {project['difficulty']}")

            print(f"\n🚀 Ready to start this project? (y/n): ", end="")
            if input().lower().startswith('y'):
                self.create_project_template(phase, project)
        else:
            print("🎉 You're ready for advanced projects! Choose your own adventure!")

    def create_project_template(self, phase, project):
        """Create project template and structure"""
        project_name = project['name'].lower().replace(' ', '_')
        project_dir = self.projects_dir / f"project_{project_name}"
        os.makedirs(project_dir, exist_ok=True)

        # Create README for the project
        readme_content = f"""# {project['name']}

## Description
{project['description']}

## Technologies Used
{', '.join(project['technologies'])}

## Getting Started
1. Open the project files in your code editor
2. Follow the step-by-step instructions below
3. Test your progress by opening index.html in a browser

## Project Structure
```
{project_name}/
├── index.html          # Main HTML file
├── styles.css          # Styling
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Tasks to Complete
- [ ] Set up basic HTML structure
- [ ] Add CSS styling
- [ ] Implement JavaScript functionality
- [ ] Test and refine
- [ ] Add personal touches

## Tips
- Take your time and understand each step
- Don't hesitate to experiment
- Ask for help when needed
- Celebrate small wins!

Good luck! 🚀
"""

        with open(project_dir / "README.md", 'w') as f:
            f.write(readme_content)

        print(f"✅ Created project template: {project_dir}")
        print(f"📖 Check the README.md file for detailed instructions!")

        # Add to completed projects
        if project['name'] not in self.progress["completed_projects"]:
            self.progress["completed_projects"].append(project['name'])
            self.save_progress()

    def review_lessons(self):
        """Review previously completed lessons"""
        print("\n📖 LESSON REVIEW")
        print("="*30)

        if not self.progress["completed_lessons"]:
            print("No lessons completed yet. Start with your first lesson!")
            return

        print("Completed lessons:")
        for lesson in self.progress["completed_lessons"]:
            print(f"✅ {lesson.replace('_', ' ').title()}")

        print(f"\n💡 Great progress! You've completed {len(self.progress['completed_lessons'])} lessons.")
        print("Keep up the excellent work!")

    def show_help(self):
        """Show help and tips"""
        print("\n❓ HELP & TIPS")
        print("="*30)

        tips = [
            "🎯 Focus on understanding, not just completing",
            "💻 Practice coding every day, even if just 15 minutes",
            "🔍 Don't be afraid to experiment and break things",
            "📚 Use online resources like MDN Web Docs",
            "🤝 Join coding communities for support",
            "🎉 Celebrate small wins and progress",
            "⏰ Take breaks when you're stuck",
            "🔄 Review previous lessons regularly"
        ]

        print("💡 Learning Tips:")
        for tip in tips:
            print(f"   {tip}")

        print(f"\n🆘 Need Help?")
        print("   • Review the lesson materials")
        print("   • Check project README files")
        print("   • Look at code examples")
        print("   • Take a break and come back fresh")

        print(f"\n🌐 Useful Resources:")
        print("   • MDN Web Docs: https://developer.mozilla.org")
        print("   • W3Schools: https://www.w3schools.com")
        print("   • freeCodeCamp: https://www.freecodecamp.org")

    def set_goals(self):
        """Set learning goals"""
        print("\n🎯 SET LEARNING GOALS")
        print("="*30)

        print("What would you like to achieve?")
        print("1. Complete next lesson this week")
        print("2. Finish current phase this month")
        print("3. Build a personal project")
        print("4. Learn a specific technology")
        print("5. Custom goal")

        choice = input("\nChoose a goal (1-5): ").strip()

        goals = {
            "1": "Complete the next lesson within 7 days",
            "2": "Finish the current phase within 30 days",
            "3": "Build and deploy a personal project",
            "4": "Master a specific technology or framework",
            "5": "Custom learning objective"
        }

        if choice in goals:
            print(f"\n✅ Goal set: {goals[choice]}")
            print("💪 You've got this! Stay consistent and keep learning!")
        else:
            print("Invalid choice. Try again!")

    def create_lesson_project(self, phase, lesson):
        """Create a basic project structure for lessons without specific content"""
        project_dir = self.projects_dir / f"phase{phase}_lesson{lesson}"
        os.makedirs(project_dir, exist_ok=True)

        # Create basic files
        html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase {phase} - Lesson {lesson}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>Phase {phase} - Lesson {lesson}</h1>
    <p>This is your workspace for this lesson.</p>
    <p>Follow the instructions and build something amazing!</p>

    <script src="script.js"></script>
</body>
</html>'''

        css_template = '''/* Phase Lesson Styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

h1 {
    color: #333;
    text-align: center;
}'''

        js_template = f'''// Phase {phase} - Lesson {lesson}
console.log("Welcome to Phase {phase}, Lesson {lesson}!");

// Your code goes here'''

        with open(project_dir / "index.html", 'w') as f:
            f.write(html_template)
        with open(project_dir / "styles.css", 'w') as f:
            f.write(css_template)
        with open(project_dir / "script.js", 'w') as f:
            f.write(js_template)

        print(f"✅ Created lesson workspace: {project_dir}")

    def run(self):
        """Main application loop"""
        self.display_welcome()

        while True:
            choice = self.display_menu()

            if choice == "1":
                self.show_current_lesson()
            elif choice == "2":
                self.show_progress()
            elif choice == "3":
                self.show_roadmap()
            elif choice == "4":
                self.start_new_project()
            elif choice == "5":
                self.review_lessons()
            elif choice == "6":
                self.show_help()
            elif choice == "7":
                self.set_goals()
            elif choice == "8":
                print("\n👋 Happy coding! See you next time!")
                break
            else:
                print("\n❌ Invalid choice. Please try again.")

            input("\nPress Enter to continue...")

if __name__ == "__main__":
    mentor = FullStackMentor()
    mentor.run()
