#!/usr/bin/env python3
"""
Real-time Code Checker
Watches your files and gives instant feedback
"""

import os
import time
import re
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class CodeAnalyzer:
    """Analyzes code and provides feedback"""
    
    def analyze_html(self, content, task_requirements=None):
        """Analyze HTML code and provide feedback"""
        feedback = []
        errors = []
        suggestions = []
        
        # Basic HTML structure checks
        if not content.strip():
            errors.append("❌ File is empty! Start by adding some HTML.")
            return {"errors": errors, "feedback": feedback, "suggestions": suggestions}
        
        # Check for DOCTYPE
        if "<!DOCTYPE html>" not in content:
            errors.append("❌ Missing <!DOCTYPE html> declaration at the top")
            suggestions.append("💡 Add <!DOCTYPE html> as the very first line")
        else:
            feedback.append("✅ Good! DOCTYPE declaration found")
        
        # Check for basic HTML structure
        if "<html>" not in content.lower():
            errors.append("❌ Missing <html> tag")
            suggestions.append("💡 Wrap your content in <html> tags")
        else:
            feedback.append("✅ HTML tag found")
        
        if "<head>" not in content.lower():
            errors.append("❌ Missing <head> section")
            suggestions.append("💡 Add a <head> section for metadata")
        else:
            feedback.append("✅ Head section found")
        
        if "<body>" not in content.lower():
            errors.append("❌ Missing <body> section")
            suggestions.append("💡 Add a <body> section for visible content")
        else:
            feedback.append("✅ Body section found")
        
        # Check for unclosed tags
        open_tags = re.findall(r'<([a-zA-Z][a-zA-Z0-9]*)[^>]*>', content)
        close_tags = re.findall(r'</([a-zA-Z][a-zA-Z0-9]*)', content)
        
        self_closing = ['img', 'br', 'hr', 'input', 'meta', 'link']
        open_tags = [tag for tag in open_tags if tag.lower() not in self_closing]
        
        unclosed = []
        for tag in open_tags:
            if tag.lower() not in [t.lower() for t in close_tags]:
                unclosed.append(tag)
        
        if unclosed:
            errors.append(f"❌ Unclosed tags found: {', '.join(unclosed)}")
            suggestions.append("💡 Make sure every opening tag has a closing tag")
        
        # Task-specific checks
        if task_requirements:
            for requirement in task_requirements:
                if requirement.lower() in content.lower():
                    feedback.append(f"✅ Found required element: {requirement}")
                else:
                    errors.append(f"❌ Missing required element: {requirement}")
        
        return {"errors": errors, "feedback": feedback, "suggestions": suggestions}
    
    def analyze_css(self, content, task_requirements=None):
        """Analyze CSS code and provide feedback"""
        feedback = []
        errors = []
        suggestions = []
        
        if not content.strip() or content.strip() == "/* Add your CSS here */":
            errors.append("❌ No CSS rules found! Add some styling.")
            return {"errors": errors, "feedback": feedback, "suggestions": suggestions}
        
        # Check for missing semicolons
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if ':' in line and not line.endswith(';') and not line.endswith('{') and not line.endswith('}') and line:
                errors.append(f"❌ Line {i}: Missing semicolon after '{line}'")
                suggestions.append("💡 CSS properties must end with semicolons")
        
        # Check for basic CSS structure
        if '{' in content and '}' in content:
            feedback.append("✅ CSS rules structure looks good")
        else:
            errors.append("❌ CSS rules need curly braces { }")
            suggestions.append("💡 CSS format: selector { property: value; }")
        
        # Task-specific checks
        if task_requirements:
            for requirement in task_requirements:
                if requirement.lower() in content.lower():
                    feedback.append(f"✅ Found required CSS: {requirement}")
                else:
                    errors.append(f"❌ Missing required CSS: {requirement}")
        
        return {"errors": errors, "feedback": feedback, "suggestions": suggestions}

class LiveCodeChecker(FileSystemEventHandler):
    """Watches files and provides real-time feedback"""
    
    def __init__(self, workspace_dir, current_task=None):
        self.workspace_dir = Path(workspace_dir)
        self.analyzer = CodeAnalyzer()
        self.current_task = current_task
        self.last_check_time = {}
        
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        # Only check files in our workspace
        if not str(file_path).startswith(str(self.workspace_dir)):
            return
        
        # Only check HTML and CSS files
        if file_path.suffix not in ['.html', '.css']:
            return
        
        # Avoid checking too frequently
        now = time.time()
        if file_path in self.last_check_time:
            if now - self.last_check_time[file_path] < 2:  # Wait 2 seconds between checks
                return
        
        self.last_check_time[file_path] = now
        
        # Small delay to ensure file is fully written
        time.sleep(0.5)
        
        self.check_file(file_path)
    
    def check_file(self, file_path):
        """Check a specific file and provide feedback"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return
        
        print(f"\n" + "="*60)
        print(f"🔍 CHECKING: {file_path.name}")
        print("="*60)
        
        # Get task requirements if available
        requirements = None
        if self.current_task and file_path.name == self.current_task.get('file'):
            requirements = self.current_task.get('expected_elements', [])
        
        # Analyze based on file type
        if file_path.suffix == '.html':
            result = self.analyzer.analyze_html(content, requirements)
        elif file_path.suffix == '.css':
            result = self.analyzer.analyze_css(content, requirements)
        else:
            return
        
        # Display feedback
        if result['errors']:
            print(f"\n❌ ISSUES FOUND:")
            for error in result['errors']:
                print(f"   {error}")
        
        if result['feedback']:
            print(f"\n✅ GOOD WORK:")
            for fb in result['feedback']:
                print(f"   {fb}")
        
        if result['suggestions']:
            print(f"\n💡 SUGGESTIONS:")
            for suggestion in result['suggestions']:
                print(f"   {suggestion}")
        
        # Overall status
        if not result['errors']:
            print(f"\n🎉 LOOKING GOOD! No errors found!")
        else:
            print(f"\n🔧 Keep working - you're getting there!")
        
        print(f"\n" + "="*60)

def start_live_checker(workspace_dir, current_task=None):
    """Start the live code checker"""
    print(f"\n🤖 LIVE CODE CHECKER STARTED")
    print(f"📁 Watching: {workspace_dir}")
    print(f"👀 I'll check your code every time you save!")
    print(f"💾 Just save your file and I'll give you instant feedback!")
    print(f"\n" + "="*50)
    
    event_handler = LiveCodeChecker(workspace_dir, current_task)
    observer = Observer()
    observer.schedule(event_handler, str(workspace_dir), recursive=True)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
        print(f"\n👋 Live checker stopped!")
    
    observer.join()

if __name__ == "__main__":
    # Test the checker
    workspace = Path(__file__).parent / "workspace"
    os.makedirs(workspace, exist_ok=True)
    
    print("🤖 Starting Live Code Checker...")
    print("📝 Create or edit HTML/CSS files in the workspace folder")
    print("💾 Save your files to see instant feedback!")
    
    start_live_checker(workspace)
