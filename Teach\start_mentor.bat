@echo off
echo.
echo ========================================
echo   FULL-STACK DEVELOPMENT MENTOR
echo ========================================
echo.
echo Starting your coding mentor...
echo.

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found Python! Starting mentor...
    python mentor.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found Python3! Starting mentor...
    python3 mentor.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Found Python launcher! Starting mentor...
    py mentor.py
    goto :end
)

echo.
echo ❌ Python not found!
echo.
echo Please install Python from: https://python.org
echo Make sure to check "Add to PATH" during installation
echo Then restart this script.
echo.
pause

:end
echo.
echo Thanks for using the Full-Stack Development Mentor!
echo.
pause
