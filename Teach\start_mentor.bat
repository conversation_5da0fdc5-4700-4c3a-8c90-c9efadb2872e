@echo off
echo.
echo ========================================
echo   INTERACTIVE CODING TUTOR
echo ========================================
echo.
echo Choose your learning mode:
echo.
echo 1. Interactive Tutor (gives tasks, checks code, instant feedback)
echo 2. Full Mentor System (comprehensive curriculum)
echo 3. Smart Tutor (advanced with live checking)
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Starting Interactive Tutor...
    goto :start_interactive
)
if "%choice%"=="2" (
    echo.
    echo Starting Full Mentor System...
    goto :start_mentor
)
if "%choice%"=="3" (
    echo.
    echo Starting Smart Tutor...
    goto :start_smart
)

echo Invalid choice. Starting Interactive Tutor by default...

:start_interactive
REM Try different Python commands for interactive tutor
python --version >nul 2>&1
if %errorlevel% == 0 (
    python interactive_tutor.py
    goto :end
)
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    python3 interactive_tutor.py
    goto :end
)
py --version >nul 2>&1
if %errorlevel% == 0 (
    py interactive_tutor.py
    goto :end
)
goto :no_python

:start_mentor
REM Try different Python commands for mentor
python --version >nul 2>&1
if %errorlevel% == 0 (
    python mentor.py
    goto :end
)
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    python3 mentor.py
    goto :end
)
py --version >nul 2>&1
if %errorlevel% == 0 (
    py mentor.py
    goto :end
)
goto :no_python

:start_smart
REM Try different Python commands for smart tutor
python --version >nul 2>&1
if %errorlevel% == 0 (
    python smart_tutor.py
    goto :end
)
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    python3 smart_tutor.py
    goto :end
)
py --version >nul 2>&1
if %errorlevel% == 0 (
    py smart_tutor.py
    goto :end
)
goto :no_python

:no_python
echo.
echo ❌ Python not found!
echo.
echo Please install Python from: https://python.org
echo Make sure to check "Add to PATH" during installation
echo Then restart this script.
echo.
pause
goto :end

:end
echo.
echo Thanks for using the Coding Tutor!
echo.
pause
