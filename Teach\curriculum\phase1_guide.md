# Phase 1: Web Fundamentals Guide

Welcome to Phase 1 of your full-stack development journey! This phase will teach you the essential building blocks of web development.

## 🎯 Phase Overview

By the end of this phase, you'll be able to:
- Create structured web pages with HTML
- Style them beautifully with CSS
- Add interactivity with JavaScript
- Use Git for version control

## 📚 Lesson Breakdown

### Lesson 1: HTML Basics
**Duration:** 2-3 hours  
**Objective:** Learn the structure and syntax of HTML

#### What You'll Learn:
- HTML document structure
- Common HTML elements (headings, paragraphs, lists, links)
- Semantic HTML for better accessibility
- Forms and input elements

#### Key Concepts:
- **Elements and Tags:** `<h1>`, `<p>`, `<div>`, etc.
- **Attributes:** `class`, `id`, `href`, `src`
- **Document Structure:** `<!DOCTYPE>`, `<html>`, `<head>`, `<body>`

#### Practice Project:
Create a personal webpage with:
- Your name and photo
- About section
- Skills list
- Contact information

---

### Lesson 2: CSS Styling
**Duration:** 3-4 hours  
**Objective:** Make your HTML look professional and beautiful

#### What You'll Learn:
- CSS syntax and selectors
- Colors, fonts, and spacing
- Box model (margin, padding, border)
- Layout techniques (flexbox basics)

#### Key Concepts:
- **Selectors:** Element, class, ID selectors
- **Properties:** `color`, `font-size`, `margin`, `padding`
- **Box Model:** Understanding how elements take up space
- **Responsive Design:** Making sites work on all devices

#### Practice Project:
Style your personal webpage with:
- Custom color scheme
- Professional typography
- Responsive layout
- Hover effects

---

### Lesson 3: JavaScript Fundamentals
**Duration:** 4-5 hours  
**Objective:** Add interactivity and dynamic behavior

#### What You'll Learn:
- Variables and data types
- Functions and events
- DOM manipulation
- Basic programming concepts

#### Key Concepts:
- **Variables:** `let`, `const`, `var`
- **Functions:** Creating reusable code blocks
- **Events:** Responding to user interactions
- **DOM:** Selecting and modifying HTML elements

#### Practice Project:
Add interactivity to your webpage:
- Click buttons to change content
- Form validation
- Dynamic content updates
- Simple animations

---

### Lesson 4: Git & Version Control
**Duration:** 2-3 hours  
**Objective:** Learn professional development workflow

#### What You'll Learn:
- Git basics and commands
- Creating repositories
- Committing changes
- Working with GitHub

#### Key Concepts:
- **Repository:** Project folder with version history
- **Commit:** Saving a snapshot of your work
- **Branch:** Working on features separately
- **Remote:** Storing code online (GitHub)

#### Practice Project:
- Initialize Git in your project
- Make commits for each feature
- Push to GitHub
- Create a live website with GitHub Pages

## 🎯 Phase 1 Final Project: Personal Portfolio

Combine everything you've learned to create a complete portfolio website:

### Requirements:
1. **HTML Structure:**
   - Header with navigation
   - About section
   - Skills/technologies section
   - Projects showcase
   - Contact form
   - Footer

2. **CSS Styling:**
   - Professional color scheme
   - Responsive design (mobile-friendly)
   - Smooth animations
   - Clean typography

3. **JavaScript Features:**
   - Interactive navigation
   - Form validation
   - Dynamic content
   - Smooth scrolling

4. **Git Workflow:**
   - Regular commits
   - Hosted on GitHub Pages
   - Clean commit history

### Success Criteria:
- ✅ Website loads without errors
- ✅ Responsive on mobile and desktop
- ✅ All interactive features work
- ✅ Code is clean and well-commented
- ✅ Deployed and accessible online

## 💡 Study Tips

1. **Practice Daily:** Even 30 minutes a day is better than cramming
2. **Build Projects:** Apply what you learn immediately
3. **Read Documentation:** Get comfortable with MDN Web Docs
4. **Join Communities:** Connect with other learners
5. **Don't Rush:** Understanding is more important than speed

## 🔗 Helpful Resources

- **HTML:** [MDN HTML Guide](https://developer.mozilla.org/en-US/docs/Web/HTML)
- **CSS:** [CSS-Tricks](https://css-tricks.com/)
- **JavaScript:** [JavaScript.info](https://javascript.info/)
- **Git:** [Git Handbook](https://guides.github.com/introduction/git-handbook/)

## 🎉 Ready to Start?

Run `python mentor.py` and select "Continue Learning" to begin your first lesson!

Remember: Every expert was once a beginner. You've got this! 🚀
