#!/usr/bin/env python3
"""
Quick Start Script for Full-Stack Development Learning
This script helps you get started quickly with your learning journey
"""

import os
import sys
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required.")
        print("Please update your Python installation.")
        return False
    return True

def welcome_message():
    """Display welcome message"""
    print("\n" + "="*60)
    print("🎓 WELCOME TO FULL-STACK DEVELOPMENT LEARNING! 🎓")
    print("="*60)
    print("\n🚀 Quick Start Guide:")
    print("1. This system will teach you web development from scratch")
    print("2. Everything is project-based and beginner-friendly")
    print("3. You'll build real applications as you learn")
    print("4. Progress is tracked automatically")
    print("\n💡 What you'll learn:")
    print("   • HTML & CSS (Web structure and styling)")
    print("   • JavaScript (Programming and interactivity)")
    print("   • React (Modern frontend development)")
    print("   • Node.js (Backend development)")
    print("   • Databases (Data storage and retrieval)")
    print("   • Full-stack projects (Complete applications)")

def check_setup():
    """Check if the learning environment is set up correctly"""
    base_dir = Path(__file__).parent
    
    print(f"\n🔍 Checking your learning environment...")
    
    # Check if mentor.py exists
    mentor_file = base_dir / "mentor.py"
    if mentor_file.exists():
        print("✅ Main mentor system found")
    else:
        print("❌ Mentor system not found")
        return False
    
    # Check if directories exist
    required_dirs = ["curriculum", "projects", "resources", "progress"]
    for dir_name in required_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/ directory ready")
        else:
            print(f"📁 Creating {dir_name}/ directory...")
            os.makedirs(dir_path, exist_ok=True)
    
    print("✅ Environment setup complete!")
    return True

def show_learning_path():
    """Show the learning path overview"""
    print(f"\n🗺️  YOUR LEARNING JOURNEY:")
    print("-" * 40)
    
    phases = [
        {
            "name": "Phase 1: Web Fundamentals",
            "duration": "4 weeks",
            "topics": ["HTML", "CSS", "JavaScript", "Git"],
            "project": "Personal Portfolio Website"
        },
        {
            "name": "Phase 2: Frontend Development", 
            "duration": "4 weeks",
            "topics": ["React", "State Management", "APIs", "Routing"],
            "project": "Interactive Todo App"
        },
        {
            "name": "Phase 3: Backend Development",
            "duration": "4 weeks", 
            "topics": ["Node.js", "Express", "Databases", "Authentication"],
            "project": "Blog API with Auth"
        },
        {
            "name": "Phase 4: Full-Stack Integration",
            "duration": "4 weeks",
            "topics": ["Frontend+Backend", "Deployment", "Testing", "Optimization"],
            "project": "E-commerce Application"
        }
    ]
    
    for i, phase in enumerate(phases, 1):
        print(f"\n📚 {phase['name']}")
        print(f"   ⏱️  Duration: {phase['duration']}")
        print(f"   🛠️  Topics: {', '.join(phase['topics'])}")
        print(f"   🎯 Project: {phase['project']}")

def get_user_info():
    """Get basic information about the user's goals"""
    print(f"\n📝 Let's personalize your learning experience!")
    print("-" * 45)
    
    questions = [
        "What's your name? (This helps personalize your experience)",
        "What's your main goal? (e.g., career change, side projects, curiosity)",
        "How much time can you dedicate per day? (e.g., 30 minutes, 1 hour, 2 hours)",
        "Do you have any programming experience? (yes/no/a little)"
    ]
    
    answers = {}
    for i, question in enumerate(questions, 1):
        answer = input(f"{i}. {question}\n   Your answer: ").strip()
        answers[f"question_{i}"] = answer
    
    return answers

def create_personalized_plan(user_info):
    """Create a personalized learning plan based on user input"""
    print(f"\n🎯 PERSONALIZED LEARNING PLAN")
    print("="*40)
    
    name = user_info.get("question_1", "Learner")
    goal = user_info.get("question_2", "learning to code")
    time_commitment = user_info.get("question_3", "1 hour")
    experience = user_info.get("question_4", "no").lower()
    
    print(f"👋 Hi {name}!")
    print(f"🎯 Goal: {goal}")
    print(f"⏰ Daily commitment: {time_commitment}")
    
    # Adjust recommendations based on experience
    if "yes" in experience or "little" in experience:
        print(f"💡 Since you have some experience, you might move through Phase 1 faster.")
        recommended_start = "You can probably complete Phase 1 in 2-3 weeks instead of 4."
    else:
        print(f"💡 As a complete beginner, take your time with the fundamentals.")
        recommended_start = "Focus on really understanding Phase 1 - it's your foundation!"
    
    print(f"📋 Recommendation: {recommended_start}")
    
    # Time-based recommendations
    if "30" in time_commitment or "minutes" in time_commitment:
        print(f"⏱️  With 30 minutes daily, expect each phase to take 5-6 weeks.")
        print(f"   Focus on consistency over speed!")
    elif "2" in time_commitment or "hours" in time_commitment:
        print(f"⏱️  With 2+ hours daily, you could complete each phase in 2-3 weeks.")
        print(f"   Don't rush - understanding is more important than speed!")
    else:
        print(f"⏱️  With 1 hour daily, the 4-week timeline per phase is perfect.")
    
    return {
        "name": name,
        "goal": goal,
        "time_commitment": time_commitment,
        "experience_level": experience,
        "recommended_pace": recommended_start
    }

def save_user_profile(user_info, plan):
    """Save user profile for the mentor system"""
    base_dir = Path(__file__).parent
    progress_dir = base_dir / "progress"
    os.makedirs(progress_dir, exist_ok=True)
    
    profile_file = progress_dir / "user_profile.txt"
    
    with open(profile_file, 'w') as f:
        f.write("FULL-STACK DEVELOPMENT LEARNER PROFILE\n")
        f.write("="*40 + "\n\n")
        f.write(f"Name: {plan['name']}\n")
        f.write(f"Goal: {plan['goal']}\n")
        f.write(f"Daily Time Commitment: {plan['time_commitment']}\n")
        f.write(f"Experience Level: {plan['experience_level']}\n")
        f.write(f"Recommended Pace: {plan['recommended_pace']}\n")
        f.write(f"\nCreated: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"✅ Profile saved! The mentor system will remember your preferences.")

def main():
    """Main function to run the quick start process"""
    # Check Python version
    if not check_python_version():
        return
    
    # Welcome message
    welcome_message()
    
    # Check setup
    if not check_setup():
        print("\n❌ Setup failed. Please check your installation.")
        return
    
    # Show learning path
    show_learning_path()
    
    # Get user information
    print(f"\n" + "="*60)
    user_info = get_user_info()
    
    # Create personalized plan
    plan = create_personalized_plan(user_info)
    
    # Save profile
    save_user_profile(user_info, plan)
    
    # Final instructions
    print(f"\n" + "="*60)
    print("🎉 SETUP COMPLETE! YOU'RE READY TO START LEARNING!")
    print("="*60)
    print(f"\n🚀 Next Steps:")
    print(f"1. Run: python mentor.py")
    print(f"2. Choose option 1: 'Continue Learning'")
    print(f"3. Start with your first HTML lesson")
    print(f"4. Build your first webpage!")
    
    print(f"\n💡 Remember:")
    print(f"   • Learning to code takes time - be patient with yourself")
    print(f"   • Focus on understanding, not just completing lessons")
    print(f"   • Build projects to apply what you learn")
    print(f"   • Don't hesitate to ask for help when stuck")
    
    print(f"\n📚 Additional Resources:")
    print(f"   • Check curriculum/ folder for detailed guides")
    print(f"   • Read resources/beginner_tips.md for learning strategies")
    print(f"   • Your progress will be automatically tracked")
    
    print(f"\n🎯 Ready to start your coding journey?")
    start_now = input("Type 'yes' to launch the mentor system now: ").strip().lower()
    
    if start_now.startswith('y'):
        print(f"\n🚀 Launching mentor system...")
        try:
            import subprocess
            subprocess.run([sys.executable, "mentor.py"], cwd=Path(__file__).parent)
        except Exception as e:
            print(f"❌ Could not launch mentor automatically.")
            print(f"Please run: python mentor.py")
    else:
        print(f"\n👋 No problem! Run 'python mentor.py' when you're ready to start.")
        print(f"Happy coding! 🎉")

if __name__ == "__main__":
    main()
