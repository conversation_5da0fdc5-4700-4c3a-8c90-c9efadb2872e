#!/usr/bin/env python3
"""
Interactive Coding Tutor - Your Personal Code Teacher
Gives you tasks, checks your work, and provides instant feedback!
"""

import os
import sys
import time
import json
from pathlib import Path

class InteractiveTutor:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.workspace_dir = self.base_dir / "workspace"
        self.progress_file = self.base_dir / "tutor_progress.json"
        
        # Create workspace
        os.makedirs(self.workspace_dir, exist_ok=True)
        
        self.load_progress()
        
    def load_progress(self):
        """Load learning progress"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                self.progress = json.load(f)
        else:
            self.progress = {
                "current_task": 1,
                "completed_tasks": [],
                "total_attempts": 0,
                "successful_completions": 0
            }
            self.save_progress()
    
    def save_progress(self):
        """Save progress"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)
    
    def welcome(self):
        """Welcome message"""
        print("\n" + "🎯"*20)
        print("🤖 INTERACTIVE CODING TUTOR")
        print("🎯"*20)
        print("\n👋 Hi! I'm your personal coding teacher!")
        print("\n✨ Here's what I do:")
        print("   📋 Give you specific coding tasks")
        print("   👀 Watch your code as you write it")
        print("   ❌ Tell you exactly what's wrong")
        print("   ✅ Celebrate when you get it right!")
        print("   💡 Give hints when you're stuck")
        print("\n🎯 Let's start coding together!")
    
    def get_task(self, task_num):
        """Get task by number"""
        tasks = {
            1: {
                "title": "Your First HTML Page",
                "description": "Create a basic HTML page with proper structure",
                "file": "index.html",
                "instructions": [
                    "1. Add <!DOCTYPE html> at the top",
                    "2. Create <html>, <head>, and <body> tags",
                    "3. Add <title>My First Page</title> in the head",
                    "4. Add <h1>Hello World!</h1> in the body"
                ],
                "check_for": [
                    "<!DOCTYPE html>",
                    "<html>",
                    "<head>",
                    "<title>My First Page</title>",
                    "<body>",
                    "<h1>Hello World!</h1>"
                ]
            },
            2: {
                "title": "Add Content to Your Page",
                "description": "Add paragraphs and lists to your HTML",
                "file": "index.html",
                "instructions": [
                    "1. Add a paragraph: 'This is my first webpage!'",
                    "2. Add an h2 heading: 'My Goals'",
                    "3. Create a list with items: 'Learn HTML', 'Learn CSS', 'Build websites'"
                ],
                "check_for": [
                    "<p>This is my first webpage!</p>",
                    "<h2>My Goals</h2>",
                    "<ul>",
                    "<li>Learn HTML</li>",
                    "<li>Learn CSS</li>",
                    "<li>Build websites</li>"
                ]
            },
            3: {
                "title": "Style Your Page with CSS",
                "description": "Create a CSS file and style your HTML",
                "file": "styles.css",
                "instructions": [
                    "1. Create a CSS file",
                    "2. Style body with: font-family: Arial, sans-serif;",
                    "3. Make h1 color: blue;",
                    "4. Add background-color: lightgray; to body"
                ],
                "check_for": [
                    "body {",
                    "font-family: Arial, sans-serif;",
                    "h1 {",
                    "color: blue;",
                    "background-color: lightgray;"
                ]
            }
        }
        return tasks.get(task_num)
    
    def present_task(self, task):
        """Present the task to the user"""
        print(f"\n" + "="*60)
        print(f"📋 TASK {self.progress['current_task']}: {task['title']}")
        print("="*60)
        print(f"\n📝 {task['description']}")
        print(f"\n🎯 Instructions:")
        for instruction in task['instructions']:
            print(f"   {instruction}")
        
        # Create the file
        file_path = self.workspace_dir / task['file']
        if not file_path.exists():
            if task['file'].endswith('.html'):
                with open(file_path, 'w') as f:
                    f.write("<!-- Start coding here! -->\n")
            elif task['file'].endswith('.css'):
                with open(file_path, 'w') as f:
                    f.write("/* Add your CSS here */\n")
        
        print(f"\n📁 Your file: {file_path}")
        print(f"💻 Open this file in your text editor and start coding!")
    
    def check_code(self, task):
        """Check if the code meets requirements"""
        file_path = self.workspace_dir / task['file']
        
        if not file_path.exists():
            print(f"\n❌ File {task['file']} not found!")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            print(f"\n❌ Could not read file!")
            return False
        
        print(f"\n🔍 Checking your {task['file']}...")
        time.sleep(1)
        
        missing = []
        found = []
        
        for requirement in task['check_for']:
            if requirement.lower() in content.lower():
                found.append(requirement)
                print(f"✅ Found: {requirement}")
            else:
                missing.append(requirement)
        
        if not missing:
            print(f"\n🎉 PERFECT! Task completed successfully!")
            print(f"🌟 You got everything right!")
            self.progress['successful_completions'] += 1
            self.mark_complete()
            return True
        else:
            print(f"\n❌ Almost there! Missing these:")
            for item in missing:
                print(f"   ❌ {item}")
            
            self.give_specific_help(missing, task)
            return False
    
    def give_specific_help(self, missing, task):
        """Give specific help for missing elements"""
        print(f"\n💡 Let me help you:")
        
        for item in missing[:3]:  # Show help for first 3 missing items
            if "<!DOCTYPE html>" in item:
                print(f"   🔧 Add <!DOCTYPE html> as the very first line")
            elif "<html>" in item:
                print(f"   🔧 Wrap everything in <html> tags")
            elif "<head>" in item:
                print(f"   🔧 Add a <head> section after <html>")
            elif "<body>" in item:
                print(f"   🔧 Add a <body> section after </head>")
            elif "<title>" in item:
                print(f"   🔧 Add <title>My First Page</title> inside the <head>")
            elif "<h1>" in item:
                print(f"   🔧 Add <h1>Hello World!</h1> inside the <body>")
            elif "<p>" in item:
                print(f"   🔧 Add the paragraph with <p> tags")
            elif "<ul>" in item:
                print(f"   🔧 Create a list with <ul> and <li> tags")
            elif "font-family" in item:
                print(f"   🔧 Add: body {{ font-family: Arial, sans-serif; }}")
            elif "color: blue" in item:
                print(f"   🔧 Add: h1 {{ color: blue; }}")
            else:
                print(f"   🔧 You need: {item}")
        
        print(f"\n🔄 Fix these and I'll check again!")
    
    def mark_complete(self):
        """Mark current task as complete"""
        task_key = f"task_{self.progress['current_task']}"
        if task_key not in self.progress['completed_tasks']:
            self.progress['completed_tasks'].append(task_key)
        
        self.progress['current_task'] += 1
        self.save_progress()
        
        print(f"\n🎊 Moving to next task...")
        time.sleep(2)
    
    def show_solution(self, task):
        """Show the complete solution"""
        print(f"\n📖 COMPLETE SOLUTION:")
        print("="*40)
        
        if task['title'] == "Your First HTML Page":
            solution = '''<!DOCTYPE html>
<html>
<head>
    <title>My First Page</title>
</head>
<body>
    <h1>Hello World!</h1>
</body>
</html>'''
        elif task['title'] == "Add Content to Your Page":
            solution = '''<!DOCTYPE html>
<html>
<head>
    <title>My First Page</title>
</head>
<body>
    <h1>Hello World!</h1>
    <p>This is my first webpage!</p>
    <h2>My Goals</h2>
    <ul>
        <li>Learn HTML</li>
        <li>Learn CSS</li>
        <li>Build websites</li>
    </ul>
</body>
</html>'''
        elif task['title'] == "Style Your Page with CSS":
            solution = '''body {
    font-family: Arial, sans-serif;
    background-color: lightgray;
}

h1 {
    color: blue;
}'''
        else:
            solution = "Solution not available"
        
        print(solution)
        print(f"\n💡 Copy this into your {task['file']} file!")
    
    def open_workspace(self):
        """Open workspace folder"""
        import subprocess
        import platform
        
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(self.workspace_dir)])
            elif platform.system() == "Darwin":
                subprocess.run(["open", str(self.workspace_dir)])
            else:
                subprocess.run(["xdg-open", str(self.workspace_dir)])
            print(f"📁 Opened workspace folder!")
        except:
            print(f"📁 Workspace: {self.workspace_dir}")
    
    def run(self):
        """Main program loop"""
        self.welcome()
        
        print(f"\n📊 Your Progress:")
        print(f"   🎯 Current Task: {self.progress['current_task']}")
        print(f"   ✅ Completed: {len(self.progress['completed_tasks'])}")
        print(f"   🏆 Success Rate: {self.progress['successful_completions']}")
        
        input(f"\nPress Enter to start your first coding task...")
        
        while True:
            task = self.get_task(self.progress['current_task'])
            if not task:
                print(f"\n🎉 Congratulations! You've completed all tasks!")
                print(f"🌟 You're ready for more advanced challenges!")
                break
            
            self.present_task(task)
            
            while True:
                print(f"\n" + "="*50)
                print(f"⚡ What would you like to do?")
                print(f"1. ✅ Check my code")
                print(f"2. 💡 Give me a hint")
                print(f"3. 👀 Show me the solution")
                print(f"4. 📁 Open workspace folder")
                print(f"5. 🔄 Start over with this task")
                print(f"6. 🚪 Exit")
                
                choice = input(f"\nChoose (1-6): ").strip()
                
                if choice == "1":
                    self.progress['total_attempts'] += 1
                    self.save_progress()
                    if self.check_code(task):
                        break  # Move to next task
                elif choice == "2":
                    print(f"\n💭 Hint: Follow the instructions step by step!")
                    print(f"💭 Make sure every opening tag has a closing tag!")
                    print(f"💭 Check your spelling and spacing carefully!")
                elif choice == "3":
                    self.show_solution(task)
                elif choice == "4":
                    self.open_workspace()
                elif choice == "5":
                    # Reset task
                    file_path = self.workspace_dir / task['file']
                    if file_path.exists():
                        file_path.unlink()
                    print(f"\n🔄 Task reset! You can start fresh.")
                    self.present_task(task)
                elif choice == "6":
                    print(f"\n👋 Great work today! Keep practicing!")
                    return
                else:
                    print(f"\n❌ Invalid choice. Try again!")

if __name__ == "__main__":
    tutor = InteractiveTutor()
    tutor.run()
