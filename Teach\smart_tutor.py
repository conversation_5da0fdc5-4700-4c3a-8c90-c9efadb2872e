#!/usr/bin/env python3
"""
Smart Interactive Coding Tutor
Gives you tasks, checks your code, and provides real-time feedback
"""

import os
import re
import time
import json
from pathlib import Path
from datetime import datetime

class SmartCodingTutor:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.workspace_dir = self.base_dir / "workspace"
        self.progress_file = self.base_dir / "tutor_progress.json"
        
        # Create workspace
        os.makedirs(self.workspace_dir, exist_ok=True)
        
        self.load_progress()
        self.current_task = None
        
    def load_progress(self):
        """Load learning progress"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                self.progress = json.load(f)
        else:
            self.progress = {
                "current_lesson": 1,
                "current_task": 1,
                "completed_tasks": [],
                "mistakes_made": [],
                "hints_used": 0,
                "start_time": datetime.now().isoformat()
            }
            self.save_progress()
    
    def save_progress(self):
        """Save progress to file"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)
    
    def welcome(self):
        """Welcome message"""
        print("\n" + "="*60)
        print("🤖 SMART INTERACTIVE CODING TUTOR")
        print("="*60)
        print("\nHi! I'm your personal coding tutor. I will:")
        print("✅ Give you specific coding tasks")
        print("✅ Check your code in real-time")
        print("✅ Tell you exactly what's wrong")
        print("✅ Guide you to the correct solution")
        print("✅ Celebrate when you get it right!")
        print("\n🎯 Let's start coding together!")
    
    def get_current_task(self):
        """Get the current task based on progress"""
        lesson = self.progress["current_lesson"]
        task_num = self.progress["current_task"]
        
        # Define tasks for each lesson
        tasks = {
            1: {  # HTML Basics
                1: {
                    "title": "Create Your First HTML Page",
                    "description": "Create a basic HTML page with a title and heading",
                    "file": "index.html",
                    "instructions": [
                        "1. Create an HTML file with DOCTYPE declaration",
                        "2. Add html, head, and body tags",
                        "3. Add a title in the head: 'My First Page'",
                        "4. Add an h1 heading in the body: 'Hello World!'"
                    ],
                    "expected_elements": ["<!DOCTYPE html>", "<html>", "<head>", "<title>My First Page</title>", "<body>", "<h1>Hello World!</h1>"],
                    "common_mistakes": {
                        "missing_doctype": "Don't forget the <!DOCTYPE html> at the top!",
                        "unclosed_tags": "Make sure all your tags are properly closed!",
                        "wrong_title": "The title should be exactly 'My First Page'",
                        "wrong_heading": "The h1 should say exactly 'Hello World!'"
                    }
                },
                2: {
                    "title": "Add Paragraphs and Lists",
                    "description": "Add content to your HTML page",
                    "file": "index.html",
                    "instructions": [
                        "1. Add a paragraph below the h1 with text: 'This is my first webpage!'",
                        "2. Add an h2 heading: 'My Favorite Things'",
                        "3. Create an unordered list with 3 items: 'Coding', 'Learning', 'Building'"
                    ],
                    "expected_elements": ["<p>This is my first webpage!</p>", "<h2>My Favorite Things</h2>", "<ul>", "<li>Coding</li>", "<li>Learning</li>", "<li>Building</li>", "</ul>"],
                    "common_mistakes": {
                        "wrong_paragraph": "The paragraph should say exactly 'This is my first webpage!'",
                        "missing_ul": "Don't forget to wrap your list items in <ul> tags!",
                        "wrong_list_items": "The list items should be: Coding, Learning, Building"
                    }
                },
                3: {
                    "title": "Add Links and Images",
                    "description": "Make your page interactive with links",
                    "file": "index.html",
                    "instructions": [
                        "1. Add a link to Google with text 'Visit Google'",
                        "2. Add an image with alt text 'Coding is fun'",
                        "3. Add a link to your email with text 'Contact Me'"
                    ],
                    "expected_elements": ["<a href=\"https://google.com\">Visit Google</a>", "<img", "alt=\"Coding is fun\"", "<a href=\"mailto:"],
                    "common_mistakes": {
                        "missing_href": "Links need an href attribute!",
                        "missing_alt": "Images should have alt text for accessibility!",
                        "wrong_link_text": "Check your link text - it should match exactly!"
                    }
                }
            },
            2: {  # CSS Basics
                1: {
                    "title": "Style Your HTML with CSS",
                    "description": "Add CSS styling to make your page look better",
                    "file": "styles.css",
                    "instructions": [
                        "1. Create a CSS file",
                        "2. Style the body with font-family: Arial, sans-serif",
                        "3. Make h1 color blue",
                        "4. Add the CSS link to your HTML file"
                    ],
                    "expected_elements": ["body {", "font-family: Arial, sans-serif", "h1 {", "color: blue"],
                    "common_mistakes": {
                        "missing_semicolon": "Don't forget semicolons after CSS properties!",
                        "wrong_selector": "Make sure you're using the right CSS selectors!",
                        "not_linked": "Did you link the CSS file to your HTML?"
                    }
                }
            }
        }
        
        if lesson in tasks and task_num in tasks[lesson]:
            return tasks[lesson][task_num]
        return None
    
    def present_task(self, task):
        """Present the current task to the user"""
        print(f"\n📋 TASK: {task['title']}")
        print("="*50)
        print(f"📝 {task['description']}")
        print(f"\n📁 File to work on: {task['file']}")
        print(f"\n🎯 Instructions:")
        for instruction in task['instructions']:
            print(f"   {instruction}")
        
        # Create the file if it doesn't exist
        file_path = self.workspace_dir / task['file']
        if not file_path.exists():
            if task['file'].endswith('.html'):
                # Create basic HTML structure
                with open(file_path, 'w') as f:
                    f.write("")
            elif task['file'].endswith('.css'):
                with open(file_path, 'w') as f:
                    f.write("/* Add your CSS here */\n")
        
        print(f"\n💻 Your workspace: {self.workspace_dir}")
        print(f"📂 Edit the file: {file_path}")
        print(f"\n⚡ When you're done, I'll check your work!")
        
        self.current_task = task
    
    def check_code(self, task):
        """Check the user's code against the task requirements"""
        file_path = self.workspace_dir / task['file']
        
        if not file_path.exists():
            print(f"\n❌ File {task['file']} not found!")
            print(f"💡 Create the file in: {self.workspace_dir}")
            return False
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n🔍 Checking your {task['file']}...")
        time.sleep(1)  # Dramatic pause
        
        missing_elements = []
        found_elements = []
        
        # Check each expected element
        for element in task['expected_elements']:
            if element.lower() in content.lower():
                found_elements.append(element)
                print(f"✅ Found: {element}")
            else:
                missing_elements.append(element)
        
        if not missing_elements:
            print(f"\n🎉 PERFECT! You completed the task correctly!")
            print(f"🌟 All required elements are present!")
            self.mark_task_complete()
            return True
        else:
            print(f"\n❌ Not quite right yet. Here's what's missing:")
            for element in missing_elements:
                print(f"   ❌ Missing: {element}")
            
            # Provide specific feedback
            self.provide_feedback(content, task, missing_elements)
            return False
    
    def provide_feedback(self, content, task, missing_elements):
        """Provide specific feedback based on common mistakes"""
        print(f"\n💡 Let me help you fix this:")
        
        # Check for common mistakes
        for mistake_key, message in task['common_mistakes'].items():
            if self.detect_mistake(content, mistake_key, task):
                print(f"   🔧 {message}")
        
        # Provide hints for missing elements
        for element in missing_elements[:2]:  # Show max 2 hints at a time
            hint = self.get_hint_for_element(element)
            if hint:
                print(f"   💭 Hint: {hint}")
        
        print(f"\n🔄 Fix these issues and I'll check again!")
        self.progress["hints_used"] += 1
        self.save_progress()
    
    def detect_mistake(self, content, mistake_type, task):
        """Detect specific types of mistakes"""
        if mistake_type == "missing_doctype":
            return "<!DOCTYPE" not in content
        elif mistake_type == "unclosed_tags":
            # Simple check for common unclosed tags
            open_tags = len(re.findall(r'<(?!/)(?!DOCTYPE)[^>]+>', content))
            close_tags = len(re.findall(r'</[^>]+>', content))
            return open_tags > close_tags + 1  # +1 for self-closing tags
        elif mistake_type == "wrong_title":
            return "My First Page" not in content
        elif mistake_type == "wrong_heading":
            return "Hello World!" not in content
        elif mistake_type == "missing_semicolon":
            # Check if CSS properties are missing semicolons
            return re.search(r':\s*[^;}\n]+\n', content) is not None
        return False
    
    def get_hint_for_element(self, element):
        """Get a helpful hint for a missing element"""
        hints = {
            "<!DOCTYPE html>": "Start your HTML file with <!DOCTYPE html> on the first line",
            "<html>": "Wrap everything in <html> tags",
            "<head>": "Add a <head> section for metadata",
            "<body>": "Add a <body> section for visible content",
            "<title>My First Page</title>": "Add <title>My First Page</title> inside the head",
            "<h1>Hello World!</h1>": "Add <h1>Hello World!</h1> inside the body",
            "<p>This is my first webpage!</p>": "Add a paragraph with <p>This is my first webpage!</p>",
            "<ul>": "Create an unordered list with <ul>",
            "</ul>": "Don't forget to close your list with </ul>",
            "font-family: Arial, sans-serif": "Set the font with: font-family: Arial, sans-serif;",
            "color: blue": "Make text blue with: color: blue;"
        }
        return hints.get(element, f"You need to add: {element}")
    
    def mark_task_complete(self):
        """Mark current task as complete and move to next"""
        task_key = f"lesson_{self.progress['current_lesson']}_task_{self.progress['current_task']}"
        if task_key not in self.progress["completed_tasks"]:
            self.progress["completed_tasks"].append(task_key)
        
        # Move to next task
        next_task = self.get_next_task()
        if next_task:
            self.progress["current_task"] = next_task["task_num"]
            if next_task["lesson_num"] != self.progress["current_lesson"]:
                self.progress["current_lesson"] = next_task["lesson_num"]
                self.progress["current_task"] = 1
        
        self.save_progress()
        
        print(f"\n🎊 Task completed! Moving to next challenge...")
        time.sleep(2)
    
    def get_next_task(self):
        """Get the next task in sequence"""
        current_lesson = self.progress["current_lesson"]
        current_task = self.progress["current_task"]
        
        # Try next task in current lesson
        if current_lesson == 1 and current_task < 3:
            return {"lesson_num": current_lesson, "task_num": current_task + 1}
        elif current_lesson == 1 and current_task == 3:
            return {"lesson_num": 2, "task_num": 1}
        
        return None  # No more tasks
    
    def run_interactive_session(self):
        """Run the main interactive tutoring session"""
        while True:
            task = self.get_current_task()
            if not task:
                print(f"\n🎉 Congratulations! You've completed all available tasks!")
                print(f"🌟 You're ready for more advanced challenges!")
                break

            self.present_task(task)

            while True:
                print(f"\n" + "="*50)
                print(f"⚡ What would you like to do?")
                print(f"1. ✅ Check my code")
                print(f"2. 🤖 Start live code checker (watches as you type!)")
                print(f"3. 💡 Give me a hint")
                print(f"4. 👀 Show me the solution")
                print(f"5. 📁 Open workspace folder")
                print(f"6. 🔄 Reset this task")
                print(f"7. 🚪 Exit")

                choice = input(f"\nChoose an option (1-7): ").strip()

                if choice == "1":
                    if self.check_code(task):
                        break  # Move to next task
                elif choice == "2":
                    self.start_live_checker(task)
                elif choice == "3":
                    self.provide_hint(task)
                elif choice == "4":
                    self.show_solution(task)
                elif choice == "5":
                    self.open_workspace()
                elif choice == "6":
                    self.reset_task(task)
                elif choice == "7":
                    print(f"\n👋 Great work today! Keep practicing!")
                    return
                else:
                    print(f"\n❌ Invalid choice. Try again!")

    def start_live_checker(self, task):
        """Start the live code checker for real-time feedback"""
        print(f"\n🤖 STARTING LIVE CODE CHECKER")
        print("="*40)
        print(f"👀 I'll watch your {task['file']} file and give you instant feedback!")
        print(f"💾 Every time you save, I'll check your code automatically!")
        print(f"🔧 I'll tell you exactly what's wrong and how to fix it!")
        print(f"\n📁 Your file: {self.workspace_dir / task['file']}")
        print(f"\n⚡ Start coding! Press Ctrl+C when you want to stop the live checker.")

        try:
            # Import the live checker
            from code_checker import start_live_checker
            start_live_checker(self.workspace_dir, task)
        except ImportError:
            print(f"\n❌ Live checker not available. Using manual checking instead.")
            print(f"💡 Save your file and choose option 1 to check your code!")
        except KeyboardInterrupt:
            print(f"\n🛑 Live checker stopped!")
            print(f"💡 You can start it again or manually check your code!")

    def reset_task(self, task):
        """Reset the current task"""
        file_path = self.workspace_dir / task['file']
        if file_path.exists():
            file_path.unlink()

        print(f"\n🔄 Task reset! File cleared.")
        print(f"💡 You can start fresh with this task!")

        # Recreate empty file
        if task['file'].endswith('.html'):
            with open(file_path, 'w') as f:
                f.write("")
        elif task['file'].endswith('.css'):
            with open(file_path, 'w') as f:
                f.write("/* Add your CSS here */\n")
    
    def provide_hint(self, task):
        """Provide a helpful hint"""
        hints = [
            f"💭 Remember to follow the instructions step by step",
            f"💭 Check that all your tags are properly opened and closed",
            f"💭 Make sure your text matches exactly what's requested",
            f"💭 Don't forget to save your file after making changes!"
        ]
        
        import random
        print(f"\n{random.choice(hints)}")
        self.progress["hints_used"] += 1
        self.save_progress()
    
    def show_solution(self, task):
        """Show the complete solution"""
        print(f"\n📖 SOLUTION for {task['title']}:")
        print("="*40)
        
        solutions = {
            "Create Your First HTML Page": '''<!DOCTYPE html>
<html>
<head>
    <title>My First Page</title>
</head>
<body>
    <h1>Hello World!</h1>
</body>
</html>''',
            "Add Paragraphs and Lists": '''<!DOCTYPE html>
<html>
<head>
    <title>My First Page</title>
</head>
<body>
    <h1>Hello World!</h1>
    <p>This is my first webpage!</p>
    <h2>My Favorite Things</h2>
    <ul>
        <li>Coding</li>
        <li>Learning</li>
        <li>Building</li>
    </ul>
</body>
</html>'''
        }
        
        solution = solutions.get(task['title'], "Solution not available yet.")
        print(solution)
        print(f"\n💡 Copy this into your {task['file']} file!")
    
    def open_workspace(self):
        """Open the workspace folder"""
        import subprocess
        import platform
        
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(self.workspace_dir)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(self.workspace_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(self.workspace_dir)])
            print(f"📁 Opened workspace folder!")
        except:
            print(f"📁 Workspace location: {self.workspace_dir}")
    
    def run(self):
        """Main entry point"""
        self.welcome()
        
        print(f"\n🎯 Current Progress:")
        print(f"   📚 Lesson: {self.progress['current_lesson']}")
        print(f"   📋 Task: {self.progress['current_task']}")
        print(f"   ✅ Completed: {len(self.progress['completed_tasks'])} tasks")
        
        input(f"\nPress Enter to start your interactive coding session...")
        
        self.run_interactive_session()

if __name__ == "__main__":
    tutor = SmartCodingTutor()
    tutor.run()
